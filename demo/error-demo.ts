import { error } from '../src'

// Demo 1: Using BaseError and concrete errors
console.log('=== Error Classes Demo ===')

class CustomError extends error.BaseError {
    public constructor(message: string, code?: string) {
        super(message, { code })
    }
}

const customError = new CustomError('Something went wrong', 'ERR001')
console.log('CustomError:', customError.toString())
console.log('CustomError JSON:', JSON.stringify(customError.toJSON(), null, 2))

const validationError = new error.ValidationError('Invalid email format', { field: 'email' })
console.log('ValidationError:', validationError.toString())
console.log('Field:', validationError.field)

const timeoutError = new error.TimeoutError('Request took too long', 5000, { retryable: true })
console.log('TimeoutError:', timeoutError.toString())
console.log('Timeout value:', timeoutError.timeout)

const notFoundError = new error.NotFoundError('User not found', { resource: 'user' })
console.log('NotFoundError:', notFoundError.toString())
console.log('Resource:', notFoundError.resource)

// Demo 2: Error utilities
console.log('\n=== Error Utilities Demo ===')

// Test isAbortError
const abortError = new DOMException('Operation was aborted', 'AbortError')
console.log('Is AbortError:', error.isAbortError(abortError))

// Test isTimeoutError
const testTimeout = new Error('Connection timeout')
console.log('Is TimeoutError:', error.isTimeoutError(testTimeout))

// Test getErrorMessage
console.log('Error message from Error:', error.getErrorMessage(new Error('Test error')))
console.log('Error message from string:', error.getErrorMessage('String error'))
console.log('Error message from object:', error.getErrorMessage({ message: 'Object error' }))
console.log('Error message from null:', error.getErrorMessage(null))

// Test isErrorLike
console.log('Is error-like (Error):', error.isErrorLike(new Error('test')))
console.log('Is error-like (object):', error.isErrorLike({ name: 'TestError', message: 'test' }))
console.log('Is error-like (string):', error.isErrorLike('error'))

// Demo 3: AggregateError
console.log('\n=== AggregateError Demo ===')

const errors = [
    new Error('First error'),
    new error.ValidationError('Invalid data'),
    new error.TimeoutError('Request timeout', 3000),
]

const aggregateError = new error.AggregateError(errors, 'Multiple errors occurred')
console.log('AggregateError:', aggregateError.message)
console.log('Number of errors:', aggregateError.errors.length)

// Demo 4: Error with cause
console.log('\n=== Error with Cause Demo ===')

const originalError = new Error('Original problem')
const wrappedError = error.createErrorWithCause('Wrapped error', originalError)
console.log('Wrapped error:', wrappedError.message)
console.log('Original cause:', wrappedError.cause)
