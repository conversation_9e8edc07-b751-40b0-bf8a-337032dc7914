import { describe, expect, test } from 'vitest'
import * as utils from '../../src/index'

describe('error module integration', () => {
    test('should be accessible from main utils export', () => {
        expect(utils.error).toBeDefined()
        expect(typeof utils.error).toBe('object')
    })

    test('should export BaseError from main utils', () => {
        expect(utils.error.BaseError).toBeDefined()
        expect(typeof utils.error.BaseError).toBe('function')
    })

    test('should export isAbortError from main utils', () => {
        expect(utils.error.isAbortError).toBeDefined()
        expect(typeof utils.error.isAbortError).toBe('function')
    })

    test('should work with BaseError from main utils', () => {
        const error = new utils.error.BaseError('Test error', { code: 'TEST' })

        expect(error.message).toBe('Test error')
        expect(error.code).toBe('TEST')
        expect(error.name).toBe('BaseError')
    })

    test('should work with isAbortError from main utils', () => {
        const abortError = new DOMException('Aborted', 'AbortError')
        const regularError = new Error('Regular')

        expect(utils.error.isAbortError(abortError)).toBe(true)
        expect(utils.error.isAbortError(regularError)).toBe(false)
    })
})