import { describe, expect, test } from 'vitest'
import * as error from '../../src/error'

describe('error module exports', () => {
    test('should export BaseError class', () => {
        expect(error.BaseError).toBeDefined()
        expect(typeof error.BaseError).toBe('function')
    })

    test('should export concrete error classes', () => {
        expect(error.ValidationError).toBeDefined()
        expect(error.TimeoutError).toBeDefined()
        expect(error.NotFoundError).toBeDefined()
        expect(error.ConfigurationError).toBeDefined()
    })

    test('should export utility functions', () => {
        expect(error.isAbortError).toBeDefined()
        expect(error.isTimeoutError).toBeDefined()
        expect(error.getErrorMessage).toBeDefined()
        expect(error.getErrorStack).toBeDefined()
        expect(error.isErrorLike).toBeDefined()
        expect(error.createErrorWithCause).toBeDefined()
    })

    test('should export AggregateError class', () => {
        expect(error.AggregateError).toBeDefined()
        expect(typeof error.AggregateError).toBe('function')
    })
})
