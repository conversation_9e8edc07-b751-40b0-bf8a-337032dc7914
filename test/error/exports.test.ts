import { describe, expect, test } from 'vitest'
import * as error from '../../src/error'

describe('error module exports', () => {
    test('should export BaseError class', () => {
        expect(error.BaseError).toBeDefined()
        expect(typeof error.BaseError).toBe('function')
    })

    test('should export isAbortError function', () => {
        expect(error.isAbortError).toBeDefined()
        expect(typeof error.isAbortError).toBe('function')
    })

    test('should export BaseErrorOptions interface', () => {
        // TypeScript interfaces don't exist at runtime, but we can test the BaseError constructor
        const testError = new error.BaseError('test', { code: 'TEST', retryable: true })

        expect(testError.code).toBe('TEST')
        expect(testError.retryable).toBe(true)
    })
})