import { describe, expect, test } from 'vitest'
import { isAbortError } from '../../src/error/errors'

describe('isAbortError', () => {
    test('should return true for AbortError', () => {
        const abortError = new DOMException('Operation was aborted', 'AbortError')

        expect(isAbortError(abortError)).toBe(true)
    })

    test('should return false for regular Error', () => {
        const error = new Error('Regular error')

        expect(isAbortError(error)).toBe(false)
    })

    test('should return false for DOMException with different name', () => {
        const domError = new DOMException('Some DOM error', 'InvalidStateError')

        expect(isAbortError(domError)).toBe(false)
    })

    test('should return false for null', () => {
        expect(isAbortError(null)).toBe(false)
    })

    test('should return false for undefined', () => {
        expect(isAbortError(undefined)).toBe(false)
    })

    test('should return false for string', () => {
        expect(isAbortError('abort error')).toBe(false)
    })

    test('should return false for object with name property', () => {
        const fakeError = { name: 'AbortError', message: 'fake' }

        expect(isAbortError(fakeError)).toBe(false)
    })

    test('should work with AbortController signal', () => {
        const controller = new AbortController()
        controller.abort()

        const promise = fetch('https://example.com', { signal: controller.signal })
            .catch(error => error)

        return promise.then(error => {
            if (error instanceof Error) {
                expect(isAbortError(error)).toBe(true)
            }
        })
    })
})
